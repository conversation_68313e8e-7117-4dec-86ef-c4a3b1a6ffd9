<template>
    <MainLayout>
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold mb-6">Payment</h1>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Payment Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white p-6 rounded shadow">
                        <h2 class="text-xl font-semibold mb-4">Payment Information</h2>
                        
                        <form @submit.prevent="submitPayment">
                            <!-- Payment Method -->
                            <div class="mb-6">
                                <h3 class="text-lg font-medium mb-3">Payment Method</h3>
                                <div class="border rounded p-4">
                                    <label class="flex items-center">
                                        <input type="radio" value="card" v-model="paymentMethod" class="mr-2">
                                        <span>Credit/Debit Card</span>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Card Information -->
                            <div v-if="paymentMethod === 'card'" class="mb-6">
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Card Number</label>
                                    <input 
                                        v-model="form.payment.card_number"
                                        type="text" 
                                        placeholder="1234 5678 9012 3456"
                                        maxlength="19"
                                        required
                                        @input="formatCardNumber"
                                        class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                </div>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
                                        <div class="grid grid-cols-2 gap-2">
                                            <select 
                                                v-model="form.payment.exp_month"
                                                required
                                                class="px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                                <option value="">MM</option>
                                                <option v-for="month in 12" :key="month" :value="month">
                                                    {{ month.toString().padStart(2, '0') }}
                                                </option>
                                            </select>
                                            <select 
                                                v-model="form.payment.exp_year"
                                                required
                                                class="px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                                <option value="">YYYY</option>
                                                <option v-for="year in years" :key="year" :value="year">
                                                    {{ year }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">CVC</label>
                                        <input 
                                            v-model="form.payment.cvc"
                                            type="text" 
                                            placeholder="123"
                                            maxlength="4"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        >
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Billing Address -->
                            <div class="mb-6">
                                <h3 class="text-lg font-medium mb-3">Billing Address</h3>
                                
                                <div class="mb-4">
                                    <label class="flex items-center">
                                        <input 
                                            type="checkbox" 
                                            v-model="form.billing.same_as_shipping"
                                            class="mr-2"
                                        >
                                        <span>Same as shipping address</span>
                                    </label>
                                </div>
                                
                                <div v-if="!form.billing.same_as_shipping" class="space-y-4">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                            <input 
                                                v-model="form.billing.first_name"
                                                type="text" 
                                                required
                                                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                            <input 
                                                v-model="form.billing.last_name"
                                                type="text" 
                                                required
                                                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                                        <input 
                                            v-model="form.billing.line_1"
                                            type="text" 
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        >
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Address Line 2 (Optional)</label>
                                        <input 
                                            v-model="form.billing.line_2"
                                            type="text"
                                            class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        >
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                            <input 
                                                v-model="form.billing.city"
                                                type="text" 
                                                required
                                                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">State</label>
                                            <input 
                                                v-model="form.billing.state"
                                                type="text" 
                                                required
                                                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                                            <input 
                                                v-model="form.billing.postcode"
                                                type="text" 
                                                required
                                                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                        </div>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                                            <input 
                                                v-model="form.billing.phone"
                                                type="tel"
                                                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                            <input 
                                                v-model="form.billing.email"
                                                type="email" 
                                                required
                                                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex gap-4">
                                <a 
                                    href="/checkout"
                                    class="flex-1 bg-gray-300 text-gray-700 py-3 rounded text-center hover:bg-gray-400"
                                >
                                    Back to Shipping
                                </a>
                                <button 
                                    type="submit"
                                    :disabled="isProcessing"
                                    class="flex-1 bg-blue-600 text-white py-3 rounded hover:bg-blue-700 disabled:opacity-50"
                                >
                                    {{ isProcessing ? 'Processing...' : 'Complete Order' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Order Summary -->
                <div class="lg:col-span-1">
                    <div class="bg-white p-6 rounded shadow">
                        <h2 class="text-xl font-semibold mb-4">Order Summary</h2>
                        
                        <div v-for="line in cart.lines" :key="line.id" class="flex items-center mb-4 pb-4 border-b">
                            <div class="w-16 h-16 bg-gray-100 rounded mr-4">
                                <img v-if="line.purchasable?.product?.thumbnail" 
                                     :src="line.purchasable.product.thumbnail.url" 
                                     alt="Product image" 
                                     class="w-full h-full object-cover rounded">
                            </div>
                            <div class="flex-1">
                                <h3 class="font-medium">{{ line.purchasable?.product?.name }}</h3>
                                <p class="text-sm text-gray-600">Qty: {{ line.quantity }}</p>
                                <p class="text-sm font-semibold">{{ line.total?.formatted }}</p>
                            </div>
                        </div>
                        
                        <div class="border-t pt-4">
                            <div class="flex justify-between py-2">
                                <span>Subtotal</span>
                                <span>{{ cart.subTotal?.formatted }}</span>
                            </div>
                            <div class="flex justify-between py-2">
                                <span>Tax</span>
                                <span>{{ cart.taxTotal?.formatted }}</span>
                            </div>
                            <div class="flex justify-between py-2 text-lg font-bold border-t">
                                <span>Total</span>
                                <span>{{ cart.total?.formatted }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </MainLayout>
</template>

<script setup>
import { defineProps, ref, computed } from 'vue';
import { router } from '@inertiajs/vue3';
import MainLayout from '../../Layouts/MainLayout.vue';

const props = defineProps({
    cart: Object
});

const isProcessing = ref(false);
const paymentMethod = ref('card');

const form = ref({
    payment: {
        card_number: '',
        exp_month: '',
        exp_year: '',
        cvc: ''
    },
    billing: {
        same_as_shipping: true,
        first_name: '',
        last_name: '',
        line_1: '',
        line_2: '',
        city: '',
        state: '',
        postcode: '',
        country_id: '',
        phone: '',
        email: ''
    }
});

const years = computed(() => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = 0; i < 10; i++) {
        years.push(currentYear + i);
    }
    return years;
});

const formatCardNumber = (event) => {
    let value = event.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
    const formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
    form.value.payment.card_number = formattedValue;
};

const submitPayment = () => {
    isProcessing.value = true;
    
    router.post('/checkout/payment', form.value, {
        onFinish: () => {
            isProcessing.value = false;
        }
    });
};
</script>
