<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Lunar\Models\Product;

class ProductController extends Controller
{
    public function index()
    {
        $products = Product::with(['variants', 'thumbnail'])->get();
        
        return Inertia::render('Products/Index', [
            'products' => $products
        ]);
    }
    
    public function show($slug)
    {
        $product = Product::whereHas('urls', function ($query) use ($slug) {
            $query->where('slug', $slug);
        })->with(['variants.prices', 'media'])->first();
        
        if (!$product) {
            abort(404);
        }
        
        return Inertia::render('Products/Show', [
            'product' => $product
        ]);
    }
}

