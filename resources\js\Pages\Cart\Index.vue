<template>
    <MainLayout>
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold mb-6">Your Shopping Cart</h1>
            
            <div v-if="cart && cart.lines.length > 0">
                <div class="bg-white rounded shadow overflow-hidden">
                    <!-- Car<PERSON> -->
                    <div class="grid grid-cols-12 gap-4 p-4 border-b bg-gray-50 font-medium text-gray-700">
                        <div class="col-span-6">Product</div>
                        <div class="col-span-2 text-center">Price</div>
                        <div class="col-span-2 text-center">Quantity</div>
                        <div class="col-span-2 text-center">Total</div>
                    </div>
                    
                    <!-- Cart Items -->
                    <div v-for="line in cart.lines" :key="line.id" class="grid grid-cols-12 gap-4 p-4 border-b items-center">
                        <div class="col-span-6 flex items-center">
                            <div class="w-16 h-16 bg-gray-100 rounded mr-4">
                                <img v-if="line.purchasable?.product?.thumbnail" 
                                     :src="line.purchasable.product.thumbnail.url" 
                                     alt="Product image" 
                                     class="w-full h-full object-cover rounded">
                            </div>
                            <div>
                                <h3 class="font-medium">{{ line.purchasable?.product?.name }}</h3>
                                <p class="text-sm text-gray-600" v-if="line.purchasable?.name">
                                    {{ line.purchasable.name }}
                                </p>
                            </div>
                        </div>
                        <div class="col-span-2 text-center">
                            {{ line.unit_price?.formatted }}
                        </div>
                        <div class="col-span-2 text-center">
                            <div class="flex items-center justify-center">
                                <button 
                                    class="bg-gray-200 px-2 py-1 rounded-l hover:bg-gray-300 disabled:opacity-50"
                                    @click="decreaseQuantity(line)"
                                    :disabled="isProcessing"
                                >
                                    -
                                </button>
                                <span class="px-2">{{ line.quantity }}</span>
                                <button 
                                    class="bg-gray-200 px-2 py-1 rounded-r hover:bg-gray-300 disabled:opacity-50"
                                    @click="increaseQuantity(line)"
                                    :disabled="isProcessing"
                                >
                                    +
                                </button>
                            </div>
                        </div>
                        <div class="col-span-2 flex items-center justify-between">
                            <button 
                                class="text-red-500 hover:text-red-700"
                                @click="removeItem(line.id)"
                                :disabled="isProcessing"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                            <span class="font-semibold">{{ line.total?.formatted }}</span>
                        </div>
                    </div>
                    
                    <!-- Cart Totals -->
                    <div class="p-4 bg-gray-50">
                        <div class="flex justify-between py-2">
                            <span>Subtotal</span>
                            <span class="font-semibold">{{ cart.subTotal?.formatted }}</span>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-200">
                            <span>Tax</span>
                            <span class="font-semibold">{{ cart.taxTotal?.formatted }}</span>
                        </div>
                        <div class="flex justify-between py-2 text-lg font-bold">
                            <span>Total</span>
                            <span>{{ cart.total?.formatted }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Checkout Button -->
                <div class="mt-6 text-right">
                    <button class="bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700">
                        Proceed to Checkout
                    </button>
                </div>
            </div>
            
            <div v-else class="text-center py-12 bg-white rounded shadow">
                <p class="text-gray-500 mb-4">Your cart is empty</p>
                <a href="/products" class="inline-block bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700">
                    Continue Shopping
                </a>
            </div>
        </div>
    </MainLayout>
</template>

<script setup>
import { defineProps, ref } from 'vue';
import { router } from '@inertiajs/vue3';
import MainLayout from '../../Layouts/MainLayout.vue';

const props = defineProps({
    cart: Object
});

const isProcessing = ref(false);

const updateQuantity = (lineId, quantity) => {
    isProcessing.value = true;
    router.post(`/cart/update/${lineId}`, { 
        quantity: quantity 
    }, {
        preserveState: true,
        preserveScroll: true,
        onFinish: () => {
            isProcessing.value = false;
        }
    });
};

const increaseQuantity = (line) => {
    updateQuantity(line.id, line.quantity + 1);
};

const decreaseQuantity = (line) => {
    if (line.quantity > 1) {
        updateQuantity(line.id, line.quantity - 1);
    } else {
        removeItem(line.id);
    }
};

const removeItem = (lineId) => {
    isProcessing.value = true;
    router.delete(`/cart/remove/${lineId}`, {
        preserveScroll: true,
        onFinish: () => {
            isProcessing.value = false;
        }
    });
};
</script>
