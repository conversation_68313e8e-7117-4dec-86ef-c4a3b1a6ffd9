<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Lunar\Facades\CartSession;
use Lunar\Models\ProductVariant;

class CartController extends Controller
{
    public function index()
    {
        $cart = CartSession::current();
        
        return Inertia::render('Cart/Index', [
            'cart' => $cart
        ]);
    }
    
    public function add(Request $request)
    {
        $validated = $request->validate([
            'variant_id' => 'required|exists:product_variants,id',
            'quantity' => 'required|integer|min:1'
        ]);
        
        $variant = ProductVariant::find($validated['variant_id']);
        
        $cart = CartSession::current();
        
        $cart->addLine($variant, $validated['quantity']);
        
        return redirect()->route('cart.index');
    }
    
    public function update(Request $request, $lineId)
    {
        $validated = $request->validate([
            'quantity' => 'required|integer|min:1'
        ]);
        
        $cart = CartSession::current();
        
        $cart->updateLine($lineId, $validated['quantity']);
        
        return redirect()->route('cart.index');
    }
    
    public function remove($lineId)
    {
        $cart = CartSession::current();
        
        $cart->removeLine($lineId);
        
        return redirect()->route('cart.index');
    }
}

