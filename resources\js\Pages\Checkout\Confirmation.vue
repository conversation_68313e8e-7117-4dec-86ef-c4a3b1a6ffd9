<template>
    <MainLayout>
        <div class="container mx-auto px-4 py-8">
            <div class="max-w-3xl mx-auto">
                <!-- Success Message -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h1 class="text-2xl font-bold text-green-800">Order Confirmed!</h1>
                            <p class="text-green-700 mt-1">Thank you for your purchase. Your order has been successfully placed.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Order Details -->
                <div class="bg-white rounded-lg shadow p-6 mb-6">
                    <h2 class="text-xl font-semibold mb-4">Order Details</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h3 class="font-medium text-gray-900 mb-2">Order Information</h3>
                            <p class="text-sm text-gray-600">Order Number: <span class="font-medium">#{{ order.reference }}</span></p>
                            <p class="text-sm text-gray-600">Order Date: <span class="font-medium">{{ formatDate(order.created_at) }}</span></p>
                            <p class="text-sm text-gray-600">Status: <span class="font-medium capitalize">{{ order.status }}</span></p>
                        </div>
                        
                        <div>
                            <h3 class="font-medium text-gray-900 mb-2">Payment Information</h3>
                            <p class="text-sm text-gray-600">Total: <span class="font-medium">{{ order.total?.formatted }}</span></p>
                            <p class="text-sm text-gray-600">Payment Status: <span class="font-medium text-green-600">Paid</span></p>
                        </div>
                    </div>
                    
                    <!-- Order Items -->
                    <div class="border-t pt-6">
                        <h3 class="font-medium text-gray-900 mb-4">Items Ordered</h3>
                        
                        <div class="space-y-4">
                            <div v-for="line in order.lines" :key="line.id" class="flex items-center">
                                <div class="w-16 h-16 bg-gray-100 rounded mr-4">
                                    <img v-if="line.purchasable?.product?.thumbnail" 
                                         :src="line.purchasable.product.thumbnail.url" 
                                         alt="Product image" 
                                         class="w-full h-full object-cover rounded">
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-medium">{{ line.purchasable?.product?.name }}</h4>
                                    <p class="text-sm text-gray-600" v-if="line.purchasable?.name">
                                        {{ line.purchasable.name }}
                                    </p>
                                    <p class="text-sm text-gray-600">Quantity: {{ line.quantity }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium">{{ line.total?.formatted }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order Totals -->
                    <div class="border-t pt-6 mt-6">
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span>Subtotal</span>
                                <span>{{ order.sub_total?.formatted }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Tax</span>
                                <span>{{ order.tax_total?.formatted }}</span>
                            </div>
                            <div class="flex justify-between text-lg font-bold border-t pt-2">
                                <span>Total</span>
                                <span>{{ order.total?.formatted }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Shipping Address -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="font-medium text-gray-900 mb-4">Shipping Address</h3>
                        <div v-if="order.shipping_address" class="text-sm text-gray-600 space-y-1">
                            <p>{{ order.shipping_address.first_name }} {{ order.shipping_address.last_name }}</p>
                            <p>{{ order.shipping_address.line_1 }}</p>
                            <p v-if="order.shipping_address.line_2">{{ order.shipping_address.line_2 }}</p>
                            <p>{{ order.shipping_address.city }}, {{ order.shipping_address.state }} {{ order.shipping_address.postcode }}</p>
                            <p v-if="order.shipping_address.phone">{{ order.shipping_address.phone }}</p>
                            <p v-if="order.shipping_address.email">{{ order.shipping_address.email }}</p>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="font-medium text-gray-900 mb-4">Billing Address</h3>
                        <div v-if="order.billing_address" class="text-sm text-gray-600 space-y-1">
                            <p>{{ order.billing_address.first_name }} {{ order.billing_address.last_name }}</p>
                            <p>{{ order.billing_address.line_1 }}</p>
                            <p v-if="order.billing_address.line_2">{{ order.billing_address.line_2 }}</p>
                            <p>{{ order.billing_address.city }}, {{ order.billing_address.state }} {{ order.billing_address.postcode }}</p>
                            <p v-if="order.billing_address.phone">{{ order.billing_address.phone }}</p>
                            <p v-if="order.billing_address.email">{{ order.billing_address.email }}</p>
                        </div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="text-center">
                    <a 
                        href="/products" 
                        class="inline-block bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700 mr-4"
                    >
                        Continue Shopping
                    </a>
                    <button 
                        @click="printOrder"
                        class="inline-block bg-gray-600 text-white px-6 py-3 rounded hover:bg-gray-700"
                    >
                        Print Order
                    </button>
                </div>
            </div>
        </div>
    </MainLayout>
</template>

<script setup>
import { defineProps } from 'vue';
import MainLayout from '../../Layouts/MainLayout.vue';

const props = defineProps({
    order: Object
});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const printOrder = () => {
    window.print();
};
</script>

<style>
@media print {
    .no-print {
        display: none !important;
    }
}
</style>
