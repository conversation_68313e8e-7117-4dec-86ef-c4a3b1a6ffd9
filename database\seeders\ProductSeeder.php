<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Lunar\Models\Brand;
use Lunar\Models\Collection;
use Lunar\Models\Currency;
use Lunar\Models\Product;
use Lunar\Models\ProductType;
use Lunar\Models\ProductVariant;
use Lunar\Models\Price;
use Lunar\Models\TaxClass;
use Lunar\Models\Url;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get or create required models
        $currency = Currency::first() ?? Currency::create([
            'code' => 'USD',
            'name' => 'US Dollar',
            'exchange_rate' => 1,
            'decimal_places' => 2,
            'enabled' => true,
            'default' => true,
        ]);

        $taxClass = TaxClass::first() ?? TaxClass::create([
            'name' => 'Default Tax Class',
        ]);

        $productType = ProductType::first() ?? ProductType::create([
            'name' => 'Physical Product',
        ]);

        $brand = Brand::first() ?? Brand::create([
            'name' => 'Sample Brand',
        ]);

        $collection = Collection::first() ?? Collection::create([
            'name' => 'Featured Products',
            'type' => 'static',
        ]);

        // Sample products data
        $products = [
            [
                'name' => 'Wireless Bluetooth Headphones',
                'description' => 'High-quality wireless headphones with noise cancellation and long battery life.',
                'price' => 9999, // $99.99 in cents
            ],
            [
                'name' => 'Smart Fitness Watch',
                'description' => 'Track your fitness goals with this advanced smartwatch featuring heart rate monitoring.',
                'price' => 19999, // $199.99 in cents
            ],
            [
                'name' => 'Portable Phone Charger',
                'description' => 'Never run out of battery with this compact and powerful portable charger.',
                'price' => 2999, // $29.99 in cents
            ],
            [
                'name' => 'Wireless Mouse',
                'description' => 'Ergonomic wireless mouse with precision tracking and long battery life.',
                'price' => 3999, // $39.99 in cents
            ],
            [
                'name' => 'USB-C Hub',
                'description' => 'Expand your connectivity with this versatile USB-C hub featuring multiple ports.',
                'price' => 4999, // $49.99 in cents
            ],
        ];

        foreach ($products as $productData) {
            // Create product
            $product = Product::create([
                'product_type_id' => $productType->id,
                'status' => 'published',
                'brand_id' => $brand->id,
            ]);

            // Add product attributes
            $product->translateAttribute('name', $productData['name']);
            $product->translateAttribute('description', $productData['description']);
            $product->save();

            // Create URL
            Url::create([
                'element_type' => Product::class,
                'element_id' => $product->id,
                'slug' => \Illuminate\Support\Str::slug($productData['name']),
                'primary' => true,
                'language_id' => null,
            ]);

            // Create variant
            $variant = ProductVariant::create([
                'product_id' => $product->id,
                'sku' => 'SKU-' . strtoupper(\Illuminate\Support\Str::random(8)),
                'tax_class_id' => $taxClass->id,
                'purchasable' => 'always',
                'stock' => 100,
                'backorder' => 0,
                'requires_shipping' => true,
                'weight_value' => 100,
                'weight_unit' => 'g',
                'height_value' => 10,
                'height_unit' => 'cm',
                'width_value' => 10,
                'width_unit' => 'cm',
                'length_value' => 10,
                'length_unit' => 'cm',
                'volume_value' => 1000,
                'volume_unit' => 'ml',
            ]);

            // Create price
            Price::create([
                'priceable_type' => ProductVariant::class,
                'priceable_id' => $variant->id,
                'currency_id' => $currency->id,
                'price' => $productData['price'],
                'min_quantity' => 1,
            ]);

            // Add to collection
            $collection->products()->attach($product->id);
        }
    }
}
