# Lunar Shop

A modern e-commerce application built with Laravel, Lunar, Inertia.js, and Vue 3.

## Features

-   🛍️ Product catalog with detailed product pages
-   🛒 Shopping cart functionality
-   💳 Complete checkout process
-   📱 Responsive design with Tailwind CSS
-   ⚡ Fast SPA experience with Inertia.js
-   🎨 Modern Vue 3 components

## Tech Stack

-   **Backend**: Laravel 10 + Lunar E-commerce
-   **Frontend**: Vue 3 + Inertia.js
-   **Styling**: Tailwind CSS
-   **Build Tool**: Vite

## Installation

1. **Clone the repository**

    ```bash
    git clone <repository-url>
    cd lunar-shop
    ```

2. **Install PHP dependencies**

    ```bash
    composer install
    ```

3. **Install Node.js dependencies**

    ```bash
    npm install
    ```

4. **Environment setup**

    ```bash
    cp .env.example .env
    php artisan key:generate
    ```

5. **Configure your database** in `.env` file:

    ```env
    DB_CONNECTION=mysql
    DB_HOST=127.0.0.1
    DB_PORT=3306
    DB_DATABASE=lunar_shop
    DB_USERNAME=your_username
    DB_PASSWORD=your_password
    ```

6. **Run migrations and install Lunar**

    ```bash
    php artisan migrate
    php artisan lunar:install
    ```

7. **Seed sample data**

    ```bash
    php artisan db:seed
    ```

8. **Build frontend assets**

    ```bash
    npm run dev
    ```

9. **Start the development server**
    ```bash
    php artisan serve
    ```

## Usage

### Customer Frontend

-   Visit `http://localhost:8000` to browse the shop
-   Add products to cart and complete checkout process

### Admin Panel

-   Visit `http://localhost:8000/admin` for the Lunar admin panel
-   Manage products, orders, and customers

## Project Structure

```
├── app/
│   ├── Http/Controllers/
│   │   ├── CartController.php      # Shopping cart operations
│   │   ├── CheckoutController.php  # Checkout process
│   │   └── ProductController.php   # Product display
│   └── Http/Middleware/
│       └── ShareCartData.php       # Global cart data sharing
├── resources/
│   ├── js/
│   │   ├── Pages/
│   │   │   ├── Cart/Index.vue      # Shopping cart page
│   │   │   ├── Checkout/           # Checkout flow pages
│   │   │   ├── Products/           # Product pages
│   │   │   └── Home.vue            # Homepage
│   │   └── Layouts/
│   │       └── MainLayout.vue      # Main site layout
│   └── css/
│       └── app.css                 # Tailwind CSS
└── routes/
    └── web.php                     # Application routes
```

## Key Features Implemented

### Shopping Cart

-   Add/remove items
-   Update quantities
-   Real-time cart totals
-   Cart count in navigation

### Checkout Process

1. **Shipping Information** - Customer address details
2. **Payment** - Credit card form (demo only)
3. **Confirmation** - Order summary and details

### Product Management

-   Product listing with pagination
-   Detailed product pages
-   Variant support
-   Image handling
-   SEO-friendly URLs

## Development

### Running in Development

```bash
# Terminal 1: Laravel development server
php artisan serve

# Terminal 2: Vite development server
npm run dev
```

### Building for Production

```bash
npm run build
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).

## Support

For support with Lunar e-commerce, visit the [Lunar documentation](https://docs.lunarphp.io/).
