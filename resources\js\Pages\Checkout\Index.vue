<template>
    <MainLayout>
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold mb-6">Checkout</h1>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Shipping Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white p-6 rounded shadow">
                        <h2 class="text-xl font-semibold mb-4">Shipping Information</h2>
                        
                        <form @submit.prevent="submitShipping">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                    <input 
                                        v-model="form.shipping.first_name"
                                        type="text" 
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                    <input 
                                        v-model="form.shipping.last_name"
                                        type="text" 
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                                <input 
                                    v-model="form.shipping.line_1"
                                    type="text" 
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                            </div>
                            
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Address Line 2 (Optional)</label>
                                <input 
                                    v-model="form.shipping.line_2"
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                    <input 
                                        v-model="form.shipping.city"
                                        type="text" 
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">State</label>
                                    <input 
                                        v-model="form.shipping.state"
                                        type="text" 
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                                    <input 
                                        v-model="form.shipping.postcode"
                                        type="text" 
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                                <select 
                                    v-model="form.shipping.country_id"
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="">Select Country</option>
                                    <option v-for="country in countries" :key="country.id" :value="country.id">
                                        {{ country.name }}
                                    </option>
                                </select>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                                    <input 
                                        v-model="form.shipping.phone"
                                        type="tel"
                                        class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input 
                                        v-model="form.shipping.email"
                                        type="email" 
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <button 
                                    type="submit"
                                    :disabled="isProcessing"
                                    class="w-full bg-blue-600 text-white py-3 rounded hover:bg-blue-700 disabled:opacity-50"
                                >
                                    {{ isProcessing ? 'Processing...' : 'Continue to Payment' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Order Summary -->
                <div class="lg:col-span-1">
                    <div class="bg-white p-6 rounded shadow">
                        <h2 class="text-xl font-semibold mb-4">Order Summary</h2>
                        
                        <div v-for="line in cart.lines" :key="line.id" class="flex items-center mb-4 pb-4 border-b">
                            <div class="w-16 h-16 bg-gray-100 rounded mr-4">
                                <img v-if="line.purchasable?.product?.thumbnail" 
                                     :src="line.purchasable.product.thumbnail.url" 
                                     alt="Product image" 
                                     class="w-full h-full object-cover rounded">
                            </div>
                            <div class="flex-1">
                                <h3 class="font-medium">{{ line.purchasable?.product?.name }}</h3>
                                <p class="text-sm text-gray-600">Qty: {{ line.quantity }}</p>
                                <p class="text-sm font-semibold">{{ line.total?.formatted }}</p>
                            </div>
                        </div>
                        
                        <div class="border-t pt-4">
                            <div class="flex justify-between py-2">
                                <span>Subtotal</span>
                                <span>{{ cart.subTotal?.formatted }}</span>
                            </div>
                            <div class="flex justify-between py-2">
                                <span>Tax</span>
                                <span>{{ cart.taxTotal?.formatted }}</span>
                            </div>
                            <div class="flex justify-between py-2 text-lg font-bold border-t">
                                <span>Total</span>
                                <span>{{ cart.total?.formatted }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </MainLayout>
</template>

<script setup>
import { defineProps, ref } from 'vue';
import { router } from '@inertiajs/vue3';
import MainLayout from '../../Layouts/MainLayout.vue';

const props = defineProps({
    cart: Object,
    countries: Array,
    states: Array
});

const isProcessing = ref(false);

const form = ref({
    shipping: {
        first_name: '',
        last_name: '',
        line_1: '',
        line_2: '',
        city: '',
        state: '',
        postcode: '',
        country_id: '',
        phone: '',
        email: ''
    }
});

const submitShipping = () => {
    isProcessing.value = true;
    
    router.post('/checkout/shipping', form.value, {
        onFinish: () => {
            isProcessing.value = false;
        }
    });
};
</script>
