<template>
    <MainLayout>
        <div class="container mx-auto px-4 py-8">
            <div v-if="product" class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Product Images -->
                <div class="bg-white p-4 rounded shadow">
                    <div v-if="product.media && product.media.length > 0" class="bg-gray-100 h-80 mb-4 rounded">
                        <img :src="product.media[0].url" alt="Product image" class="w-full h-full object-contain rounded">
                    </div>
                    <div v-else class="bg-gray-100 h-80 mb-4 rounded flex items-center justify-center">
                        <span class="text-gray-400">No image available</span>
                    </div>
                    
                    <!-- Thumbnails -->
                    <div v-if="product.media && product.media.length > 1" class="grid grid-cols-4 gap-2">
                        <div v-for="media in product.media" :key="media.id" class="bg-gray-100 h-20 rounded cursor-pointer">
                            <img :src="media.url" alt="Product thumbnail" class="w-full h-full object-cover rounded">
                        </div>
                    </div>
                </div>
                
                <!-- Product Details -->
                <div>
                    <h1 class="text-3xl font-bold mb-2">{{ product.name }}</h1>
                    <div v-if="selectedVariant && selectedVariant.prices.length > 0" class="text-2xl font-bold text-blue-600 mb-4">
                        {{ selectedVariant.prices[0]?.price?.formatted }}
                    </div>
                    
                    <div class="mb-6" v-html="product.description"></div>
                    
                    <!-- Variants Selector -->
                    <div v-if="product.variants.length > 1" class="mb-6">
                        <h3 class="text-lg font-semibold mb-2">Options</h3>
                        <div class="grid grid-cols-2 gap-2">
                            <div v-for="variant in product.variants" :key="variant.id" 
                                 @click="selectedVariant = variant"
                                 :class="[
                                     'border p-2 rounded cursor-pointer hover:bg-gray-50',
                                     selectedVariant && selectedVariant.id === variant.id ? 'border-blue-500 bg-blue-50' : ''
                                 ]">
                                {{ variant.name || 'Default Variant' }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quantity -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-2">Quantity</h3>
                        <div class="flex items-center">
                            <button @click="quantity > 1 && quantity--" class="bg-gray-200 px-3 py-1 rounded-l">-</button>
                            <input type="number" v-model.number="quantity" min="1" class="border-t border-b px-3 py-1 w-16 text-center" />
                            <button @click="quantity++" class="bg-gray-200 px-3 py-1 rounded-r">+</button>
                        </div>
                    </div>
                    
                    <!-- Add to Cart -->
                    <div class="mt-6">
                        <form @submit.prevent="addToCart">
                            <button type="submit" class="bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700 w-full md:w-auto">
                                Add to Cart
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div v-else class="text-center py-8">
                <p class="text-gray-500">Product not found</p>
            </div>
        </div>
    </MainLayout>
</template>

<script setup>
import { defineProps, ref, computed } from 'vue';
import { router } from '@inertiajs/vue3';
import MainLayout from '../../Layouts/MainLayout.vue';

const props = defineProps({
    product: Object
});

const quantity = ref(1);
const selectedVariant = ref(props.product?.variants[0] || null);

const addToCart = () => {
    if (!selectedVariant.value) return;
    
    router.post('/cart/add', {
        variant_id: selectedVariant.value.id,
        quantity: quantity.value
    });
};
</script>

