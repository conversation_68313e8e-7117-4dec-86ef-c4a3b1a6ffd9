<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Lunar\Facades\CartSession;
use Lunar\Models\Cart;
use Lunar\Models\Country;
use Lunar\Models\Currency;
use Lunar\Models\Order;
use Lunar\Models\State;

class CheckoutController extends Controller
{
    /**
     * Show the checkout page.
     *
     * @return \Inertia\Response
     */
    public function index()
    {
        $cart = CartSession::current();

        if (!$cart || count($cart->lines) === 0) {
            return redirect()->route('cart.index');
        }

        $countries = Country::all();
        $states = State::all();

        return Inertia::render('Checkout/Index', [
            'cart' => $cart->toArray(),
            'countries' => $countries,
            'states' => $states,
        ]);
    }

    /**
     * Handle shipping information submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function shipping(Request $request)
    {
        $validated = $request->validate([
            'shipping.first_name' => 'required|string|max:255',
            'shipping.last_name' => 'required|string|max:255',
            'shipping.line_1' => 'required|string|max:255',
            'shipping.line_2' => 'nullable|string|max:255',
            'shipping.city' => 'required|string|max:255',
            'shipping.state' => 'required|string|max:255',
            'shipping.postcode' => 'required|string|max:20',
            'shipping.country_id' => 'required|exists:countries,id',
            'shipping.phone' => 'nullable|string|max:20',
            'shipping.email' => 'required|email|max:255',
        ]);

        $cart = CartSession::current();
        $cart->setShippingAddress($validated['shipping']);
        $cart->save();

        return redirect()->route('checkout.payment');
    }

    /**
     * Show the payment page.
     *
     * @return \Inertia\Response
     */
    public function payment()
    {
        $cart = CartSession::current();

        if (!$cart || count($cart->lines) === 0) {
            return redirect()->route('cart.index');
        }

        if (!$cart->shipping_address) {
            return redirect()->route('checkout.index');
        }

        return Inertia::render('Checkout/Payment', [
            'cart' => $cart->toArray(),
        ]);
    }

    /**
     * Process the payment and create an order.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processPayment(Request $request)
    {
        $validated = $request->validate([
            'payment.card_number' => 'required|string|max:19',
            'payment.exp_month' => 'required|numeric|min:1|max:12',
            'payment.exp_year' => 'required|numeric|min:2023',
            'payment.cvc' => 'required|string|max:4',
            'billing.same_as_shipping' => 'required|boolean',
            'billing.first_name' => 'required_if:billing.same_as_shipping,false|string|max:255',
            'billing.last_name' => 'required_if:billing.same_as_shipping,false|string|max:255',
            'billing.line_1' => 'required_if:billing.same_as_shipping,false|string|max:255',
            'billing.line_2' => 'nullable|string|max:255',
            'billing.city' => 'required_if:billing.same_as_shipping,false|string|max:255',
            'billing.state' => 'required_if:billing.same_as_shipping,false|string|max:255',
            'billing.postcode' => 'required_if:billing.same_as_shipping,false|string|max:20',
            'billing.country_id' => 'required_if:billing.same_as_shipping,false|exists:countries,id',
            'billing.phone' => 'nullable|string|max:20',
            'billing.email' => 'required_if:billing.same_as_shipping,false|email|max:255',
        ]);

        $cart = CartSession::current();

        if ($validated['billing']['same_as_shipping']) {
            $cart->setBillingAddress($cart->shipping_address->toArray());
        } else {
            $cart->setBillingAddress($validated['billing']);
        }
        $cart->save();

        // In a real application, you would integrate with a payment gateway here
        // For this example, we'll just create the order without actual payment processing

        try {
            $order = $cart->createOrder();
            // Mark the order as paid (this would normally happen after payment confirmation)
            $order->update(['status' => 'payment-received']);
            
            // Clear the cart session
            CartSession::forget();

            return redirect()->route('checkout.confirmation', ['order' => $order->id]);
        } catch (\Exception $e) {
            return redirect()->back()->withErrors(['payment' => 'There was an error processing your payment. Please try again.']);
        }
    }

    /**
     * Show the order confirmation page.
     *
     * @param  \Lunar\Models\Order  $order
     * @return \Inertia\Response
     */
    public function confirmation(Order $order)
    {
        return Inertia::render('Checkout/Confirmation', [
            'order' => $order->load(['lines', 'shippingAddress', 'billingAddress'])->toArray(),
        ]);
    }
}

