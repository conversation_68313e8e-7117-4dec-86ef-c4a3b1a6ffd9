<template>
    <MainLayout>
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold mb-4">Welcome to Lunar Shop</h1>
            <p class="mb-4">
                Your e-commerce shop built with Laravel, Lunar, Inertia.js and
                Vue 3
            </p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div
                    v-for="product in featuredProducts"
                    :key="product.id"
                    class="bg-white p-4 rounded shadow"
                >
                    <div class="bg-gray-200 h-40 mb-2 rounded">
                        <img
                            v-if="product.thumbnail"
                            :src="product.thumbnail.url"
                            alt="Product image"
                            class="w-full h-full object-cover rounded"
                        />
                    </div>
                    <h2 class="text-xl font-semibold">{{ product.name }}</h2>
                    <p class="text-gray-600">
                        {{ product.description || "Featured product" }}
                    </p>
                    <p
                        v-if="
                            product.variants.length > 0 &&
                            product.variants[0].prices.length > 0
                        "
                        class="text-lg font-bold mt-2"
                    >
                        {{ product.variants[0].prices[0]?.price?.formatted }}
                    </p>
                    <a
                        :href="`/products/${
                            product.urls[0]?.slug || product.id
                        }`"
                        class="inline-block mt-2 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                    >
                        View Product
                    </a>
                </div>
            </div>

            <div class="mt-12 text-center">
                <a
                    href="/products"
                    class="inline-block bg-gray-800 text-white px-8 py-3 rounded hover:bg-gray-700"
                >
                    View All Products
                </a>
            </div>
        </div>
    </MainLayout>
</template>

<script setup>
import { defineProps } from "vue";
import MainLayout from "../Layouts/MainLayout.vue";

const props = defineProps({
    featuredProducts: {
        type: Array,
        default: () => [],
    },
});
</script>
