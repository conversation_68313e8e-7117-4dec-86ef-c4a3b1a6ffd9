<template>
    <MainLayout>
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold mb-4">Welcome to Lunar Shop</h1>
            <p class="mb-4">Your e-commerce shop built with Laravel, Lunar, Inertia.js and Vue 3</p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div v-for="n in 3" :key="n" class="bg-white p-4 rounded shadow">
                    <div class="bg-gray-200 h-40 mb-2 rounded"></div>
                    <h2 class="text-xl font-semibold">Product {{ n }}</h2>
                    <p class="text-gray-600">Sample product description</p>
                    <p class="text-lg font-bold mt-2">$19.99</p>
                    <button class="mt-2 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        Add to Cart
                    </button>
                </div>
            </div>
        </div>
    </MainLayout>
</template>

<script setup>
import MainLayout from '../Layouts/MainLayout.vue';
// Component logic goes here
</script>

