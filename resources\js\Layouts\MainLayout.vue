<template>
    <div>
        <header class="bg-gray-800 text-white shadow">
            <div class="container mx-auto px-4 py-4 flex justify-between items-center">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold">Lunar Shop</h1>
                </div>
                <nav>
                    <ul class="flex space-x-6">
                        <li><a href="/" class="hover:text-gray-300">Home</a></li>
                        <li><a href="/products" class="hover:text-gray-300">Products</a></li>
                        <li><a href="/cart" class="hover:text-gray-300">Cart</a></li>
                    </ul>
                </nav>
            </div>
        </header>
        
        <main>
            <slot />
        </main>
        
        <footer class="bg-gray-800 text-white mt-8 py-6">
            <div class="container mx-auto px-4">
                <div class="flex flex-col md:flex-row justify-between">
                    <div class="mb-4 md:mb-0">
                        <h3 class="text-xl font-bold mb-2">Lunar Shop</h3>
                        <p>Your premier e-commerce destination</p>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold mb-2">Contact</h4>
                        <p>Email: <EMAIL></p>
                        <p>Phone: (*************</p>
                    </div>
                </div>
                <div class="mt-6 pt-6 border-t border-gray-700 text-center">
                    <p>&copy; {{ new Date().getFullYear() }} Lunar Shop. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>
</template>

<script setup>
// Layout component logic here
</script>

